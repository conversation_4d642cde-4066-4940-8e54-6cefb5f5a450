package models

import (
	"net/http"
	"time"

	"github.com/gorilla/mux"
	"github.com/portainer/portainer-ee/api/dataservices"
	"github.com/portainer/portainer-ee/api/docker"
	"github.com/portainer/portainer-ee/api/http/middlewares"
	"github.com/portainer/portainer-ee/api/http/security"
	httperror "github.com/portainer/portainer/pkg/libhttp/error"
	"github.com/portainer/portainer/pkg/libhttp/response"
)

type Handler struct {
	*mux.Router
	modelService *docker.ModelService
	dataStore    dataservices.DataStore
	bouncer      security.BouncerService
}

// ModelListResponse represents the response for model list endpoint
type ModelListResponse struct {
	ModelName    string    `json:"model_name"`
	Parameters   string    `json:"parameters"`
	Quantization string    `json:"quantization"`
	Architecture string    `json:"architecture"`
	ModelID      string    `json:"model_id"`
	Created      time.Time `json:"created"`
	Size         string    `json:"size"`
	Status       string    `json:"status"`
}

// ModelRunnerStatusResponse represents the response for model runner status endpoint
type ModelRunnerStatusResponse struct {
	Running bool   `json:"running"`
	Status  string `json:"status"`
}

// ModelInspectResponse represents the response for model inspect endpoint
type ModelInspectResponse struct {
	ID       string `json:"id"`
	Tags     string `json:"tags"`
	Created  string `json:"created"`
	Config   struct {
		Parameters   string `json:"parameters"`
		Quantization string `json:"quantization"`
		Architecture string `json:"architecture"`
		Size         string `json:"size"`
	} `json:"config"`
}

// NewHandler creates a handler to process Docker model management requests
func NewHandler(routePrefix string, bouncer security.BouncerService, dataStore dataservices.DataStore, modelService *docker.ModelService) *Handler {
	h := &Handler{
		Router:       mux.NewRouter(),
		modelService: modelService,
		dataStore:    dataStore,
		bouncer:      bouncer,
	}

	router := h.PathPrefix(routePrefix).Subrouter()
	router.Use(bouncer.AuthenticatedAccess, middlewares.CheckEndpointAuthorization(bouncer))

	// Model list endpoints
	router.Handle("", httperror.LoggerHandler(h.modelsList)).Methods(http.MethodGet)

	// Model runner management endpoints
	router.Handle("/runner/status", httperror.LoggerHandler(h.modelRunnerStatus)).Methods(http.MethodGet)
	router.Handle("/runner/install", httperror.LoggerHandler(h.installModelRunner)).Methods(http.MethodPost)
	router.Handle("/runner/uninstall", httperror.LoggerHandler(h.uninstallModelRunner)).Methods(http.MethodPost)

	// Individual model action endpoints
	router.Handle("/{modelName}/unload", httperror.LoggerHandler(h.unloadModel)).Methods(http.MethodPost)
	router.Handle("/{modelName}/remove", httperror.LoggerHandler(h.removeModel)).Methods(http.MethodDelete)
	router.Handle("/{modelName}/inspect", httperror.LoggerHandler(h.inspectModel)).Methods(http.MethodGet)

	return h
}

// @id modelsList
// @summary List models
// @description List all Docker models using 'docker model ls' and 'docker model ps' commands
// @description **Access policy**: authenticated
// @tags docker
// @security jwt
// @accept json
// @produce json
// @param id path int true "Environment identifier"
// @success 200 {array} ModelListResponse "Success"
// @failure 400 "Invalid request"
// @failure 404 "Environment not found"
// @failure 500 "Server error"
// @router /docker/{id}/models [get]
func (handler *Handler) modelsList(w http.ResponseWriter, r *http.Request) *httperror.HandlerError {
	endpoint, err := middlewares.FetchEndpoint(r)
	if err != nil {
		return httperror.NotFound("Unable to find an environment on request context", err)
	}

	if err := handler.bouncer.AuthorizedEndpointOperation(r, endpoint, true); err != nil {
		return httperror.Forbidden("Permission denied to access environment", err)
	}

	// Get all models
	allModels, err := handler.modelService.ListModels(endpoint)
	if err != nil {
		return httperror.InternalServerError("Unable to retrieve models", err)
	}

	// Get running models
	runningModels, err := handler.modelService.ListRunningModels(endpoint)
	if err != nil {
		return httperror.InternalServerError("Unable to retrieve running models", err)
	}

	// Create a map of running models for quick lookup
	runningMap := make(map[string]bool)
	for _, running := range runningModels {
		runningMap[running.ModelID] = true
	}

	// Transform to response format
	var responseData []ModelListResponse
	for _, model := range allModels {
		status := "stopped"
		if runningMap[model.ModelID] {
			status = "running"
		}

		responseData = append(responseData, ModelListResponse{
			ModelName:    model.ModelName,
			Parameters:   model.Parameters,
			Quantization: model.Quantization,
			Architecture: model.Architecture,
			ModelID:      model.ModelID,
			Created:      model.Created,
			Size:         model.Size,
			Status:       status,
		})
	}

	return response.JSON(w, responseData)
}

// @id modelRunnerStatus
// @summary Get model runner status
// @description Get the status of the Docker model runner using 'docker model status' command
// @description **Access policy**: authenticated
// @tags docker
// @security jwt
// @accept json
// @produce json
// @param id path int true "Environment identifier"
// @success 200 {object} ModelRunnerStatusResponse "Success"
// @failure 400 "Invalid request"
// @failure 404 "Environment not found"
// @failure 500 "Server error"
// @router /docker/{id}/models/runner/status [get]
func (handler *Handler) modelRunnerStatus(w http.ResponseWriter, r *http.Request) *httperror.HandlerError {
	endpoint, err := middlewares.FetchEndpoint(r)
	if err != nil {
		return httperror.NotFound("Unable to find an environment on request context", err)
	}

	if err := handler.bouncer.AuthorizedEndpointOperation(r, endpoint, true); err != nil {
		return httperror.Forbidden("Permission denied to access environment", err)
	}

	status, err := handler.modelService.GetModelRunnerStatus(endpoint)
	if err != nil {
		return httperror.InternalServerError("Unable to retrieve model runner status", err)
	}

	responseData := ModelRunnerStatusResponse{
		Running: status.Running,
		Status:  status.Status,
	}

	return response.JSON(w, responseData)
}

// @id installModelRunner
// @summary Install model runner
// @description Install the Docker model runner using 'docker model install-runner' command
// @description **Access policy**: authenticated
// @tags docker
// @security jwt
// @accept json
// @produce json
// @param id path int true "Environment identifier"
// @success 200 "Success"
// @failure 400 "Invalid request"
// @failure 404 "Environment not found"
// @failure 500 "Server error"
// @router /docker/{id}/models/runner/install [post]
func (handler *Handler) installModelRunner(w http.ResponseWriter, r *http.Request) *httperror.HandlerError {
	endpoint, err := middlewares.FetchEndpoint(r)
	if err != nil {
		return httperror.NotFound("Unable to find an environment on request context", err)
	}

	if err := handler.bouncer.AuthorizedEndpointOperation(r, endpoint, true); err != nil {
		return httperror.Forbidden("Permission denied to access environment", err)
	}

	err = handler.modelService.InstallModelRunner(endpoint)
	if err != nil {
		return httperror.InternalServerError("Unable to install model runner", err)
	}

	return response.Empty(w)
}

// @id uninstallModelRunner
// @summary Uninstall model runner
// @description Uninstall the Docker model runner using 'docker model uninstall-runner' command
// @description **Access policy**: authenticated
// @tags docker
// @security jwt
// @accept json
// @produce json
// @param id path int true "Environment identifier"
// @success 200 "Success"
// @failure 400 "Invalid request"
// @failure 404 "Environment not found"
// @failure 500 "Server error"
// @router /docker/{id}/models/runner/uninstall [post]
func (handler *Handler) uninstallModelRunner(w http.ResponseWriter, r *http.Request) *httperror.HandlerError {
	endpoint, err := middlewares.FetchEndpoint(r)
	if err != nil {
		return httperror.NotFound("Unable to find an environment on request context", err)
	}

	if err := handler.bouncer.AuthorizedEndpointOperation(r, endpoint, true); err != nil {
		return httperror.Forbidden("Permission denied to access environment", err)
	}

	err = handler.modelService.UninstallModelRunner(endpoint)
	if err != nil {
		return httperror.InternalServerError("Unable to uninstall model runner", err)
	}

	return response.Empty(w)
}

// @id unloadModel
// @summary Unload model
// @description Unload (stop) a Docker model using 'docker model unload' command
// @description **Access policy**: authenticated
// @tags docker
// @security jwt
// @accept json
// @produce json
// @param id path int true "Environment identifier"
// @param modelName path string true "Model name"
// @success 200 "Success"
// @failure 400 "Invalid request"
// @failure 404 "Environment not found"
// @failure 500 "Server error"
// @router /docker/{id}/models/{modelName}/unload [post]
func (handler *Handler) unloadModel(w http.ResponseWriter, r *http.Request) *httperror.HandlerError {
	endpoint, err := middlewares.FetchEndpoint(r)
	if err != nil {
		return httperror.NotFound("Unable to find an environment on request context", err)
	}

	if err := handler.bouncer.AuthorizedEndpointOperation(r, endpoint, true); err != nil {
		return httperror.Forbidden("Permission denied to access environment", err)
	}

	vars := mux.Vars(r)
	modelName := vars["modelName"]
	if modelName == "" {
		return httperror.BadRequest("Invalid model name", nil)
	}

	err = handler.modelService.UnloadModel(endpoint, modelName)
	if err != nil {
		return httperror.InternalServerError("Unable to unload model", err)
	}

	return response.Empty(w)
}

// @id removeModel
// @summary Remove model
// @description Remove a Docker model using 'docker model rm' command
// @description **Access policy**: authenticated
// @tags docker
// @security jwt
// @accept json
// @produce json
// @param id path int true "Environment identifier"
// @param modelName path string true "Model name"
// @success 200 "Success"
// @failure 400 "Invalid request"
// @failure 404 "Environment not found"
// @failure 500 "Server error"
// @router /docker/{id}/models/{modelName}/remove [delete]
func (handler *Handler) removeModel(w http.ResponseWriter, r *http.Request) *httperror.HandlerError {
	endpoint, err := middlewares.FetchEndpoint(r)
	if err != nil {
		return httperror.NotFound("Unable to find an environment on request context", err)
	}

	if err := handler.bouncer.AuthorizedEndpointOperation(r, endpoint, true); err != nil {
		return httperror.Forbidden("Permission denied to access environment", err)
	}

	vars := mux.Vars(r)
	modelName := vars["modelName"]
	if modelName == "" {
		return httperror.BadRequest("Invalid model name", nil)
	}

	err = handler.modelService.RemoveModel(endpoint, modelName)
	if err != nil {
		return httperror.InternalServerError("Unable to remove model", err)
	}

	return response.Empty(w)
}

// @id inspectModel
// @summary Inspect model
// @description Get detailed information about a Docker model using 'docker model inspect' command
// @description **Access policy**: authenticated
// @tags docker
// @security jwt
// @accept json
// @produce json
// @param id path int true "Environment identifier"
// @param modelName path string true "Model name"
// @success 200 {object} ModelInspectResponse "Success"
// @failure 400 "Invalid request"
// @failure 404 "Environment not found"
// @failure 500 "Server error"
// @router /docker/{id}/models/{modelName}/inspect [get]
func (handler *Handler) inspectModel(w http.ResponseWriter, r *http.Request) *httperror.HandlerError {
	endpoint, err := middlewares.FetchEndpoint(r)
	if err != nil {
		return httperror.NotFound("Unable to find an environment on request context", err)
	}

	if err := handler.bouncer.AuthorizedEndpointOperation(r, endpoint, true); err != nil {
		return httperror.Forbidden("Permission denied to access environment", err)
	}

	vars := mux.Vars(r)
	modelName := vars["modelName"]
	if modelName == "" {
		return httperror.BadRequest("Invalid model name", nil)
	}

	result, err := handler.modelService.InspectModel(endpoint, modelName)
	if err != nil {
		return httperror.InternalServerError("Unable to inspect model", err)
	}

	responseData := ModelInspectResponse{
		ID:      result.ID,
		Tags:    result.Tags,
		Created: result.Created,
		Config:  result.Config,
	}

	return response.JSON(w, responseData)
}
