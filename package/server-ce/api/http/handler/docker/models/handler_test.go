package models

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// Test response type structures
func TestModelListResponseStruct(t *testing.T) {
	now := time.Now()
	response := ModelListResponse{
		ModelName:    "test-model",
		Parameters:   "1B",
		Quantization: "Q4_0",
		Architecture: "llama",
		ModelID:      "abc123",
		Created:      now,
		Size:         "1.5GB",
		Status:       "running",
	}

	assert.Equal(t, "test-model", response.ModelName)
	assert.Equal(t, "1B", response.Parameters)
	assert.Equal(t, "Q4_0", response.Quantization)
	assert.Equal(t, "llama", response.Architecture)
	assert.Equal(t, "abc123", response.ModelID)
	assert.Equal(t, now, response.Created)
	assert.Equal(t, "1.5GB", response.Size)
	assert.Equal(t, "running", response.Status)
}

func TestModelRunnerStatusResponseStruct(t *testing.T) {
	response := ModelRunnerStatusResponse{
		Running: true,
		Status:  "active",
	}

	assert.True(t, response.Running)
	assert.Equal(t, "active", response.Status)
}

func TestModelInspectResponseStruct(t *testing.T) {
	response := ModelInspectResponse{
		ID:      "ghi789",
		Tags:    "latest",
		Created: "2023-01-01T00:00:00Z",
		Config: struct {
			Parameters   string `json:"parameters"`
			Quantization string `json:"quantization"`
			Architecture string `json:"architecture"`
			Size         string `json:"size"`
		}{
			Parameters:   "7B",
			Quantization: "Q8_0",
			Architecture: "transformer",
			Size:         "4.2GB",
		},
	}

	assert.Equal(t, "ghi789", response.ID)
	assert.Equal(t, "latest", response.Tags)
	assert.Equal(t, "2023-01-01T00:00:00Z", response.Created)
	assert.Equal(t, "7B", response.Config.Parameters)
	assert.Equal(t, "Q8_0", response.Config.Quantization)
	assert.Equal(t, "transformer", response.Config.Architecture)
	assert.Equal(t, "4.2GB", response.Config.Size)
}

// Note: Full integration tests for the handlers would require setting up
// complete mock implementations of all dependencies (BouncerService, DataStore, etc.)
// which is complex and would be better suited for integration test suites.
// The above tests verify the response structures and basic functionality.
