import { ResourceControlViewModel } from '@/react/portainer/access-control/models/ResourceControlViewModel';

export enum ModelStatus {
  Running = 'running',
  Stopped = 'stopped',
}

export type QuickAction = 'inspect' | 'logs' | 'stats' | 'console';

export interface ModelPort {
  host?: string;
  public: number;
  private: number;
}

export type ModelId = string;

/**
 * Model List ViewModel
 *
 * Represents a Docker model from 'docker model ls' command
 */
export interface ModelListViewModel {
  // Core Docker model properties (matching docker model ls output)
  ModelName: string; // MODEL NAME column
  Parameters: string; // PARAMETERS column
  Quantization: string; // QUANTIZATION column
  Architecture: string; // ARCHITECTURE column
  ModelId: string; // MODEL ID column
  Created: number; // CREATED column (timestamp)
  Size: string; // SIZE column

  // Additional properties for UI functionality
  Status: ModelStatus;
  StatusText: string;
  NodeName: string;
  ResourceControl?: ResourceControlViewModel;
  IsPortainer: boolean;

  // Legacy properties for compatibility (can be derived from ModelName/ModelId)
  Id: ModelId;
  Names: string[];
}

export type ModelLogsParams = {
  stdout?: boolean;
  stderr?: boolean;
  timestamps?: boolean;
  since?: number;
  tail?: number;
};
