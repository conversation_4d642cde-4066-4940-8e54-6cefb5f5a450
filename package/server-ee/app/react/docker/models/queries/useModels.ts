import { useQuery } from '@tanstack/react-query';

import { EnvironmentId } from '@/react/portainer/environments/types';
import { withGlobalError } from '@/react-tools/react-query';

import { ModelListViewModel } from '../types';
import { getModels } from '../models.service';

import { queryKeys } from './query-keys';

interface UseModels {
  all?: boolean;
  nodeName?: string;
}

export function useModels<T = ModelListViewModel[]>(
  environmentId: EnvironmentId,
  {
    autoRefreshRate,
    select,
    enabled,
    ...params
  }: UseModels & {
    autoRefreshRate?: number;
    select?: (data: ModelListViewModel[]) => T;
    enabled?: boolean;
  } = {}
) {
  return useQuery(
    queryKeys.filters(environmentId, params),
    () => getModels(environmentId, params),
    {
      ...withGlobalError('Unable to retrieve models'),
      refetchInterval: autoRefreshRate ?? false,
      select,
      enabled,
    }
  );
}

// The getModels function is now imported from models.service.ts
