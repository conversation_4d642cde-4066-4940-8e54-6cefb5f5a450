import { useState } from 'react';
import { RefreshCw, Square, Trash2, Download, Upload } from 'lucide-react';

import UpToDate from '@/assets/ico/icon_up-to-date.svg?c';
import UpdatesUnknown from '@/assets/ico/icon_updates-unknown.svg?c';
import { useAuthorizations, Authorized } from '@/react/hooks/useUser';
import {
  ModelListViewModel,
  ModelStatus,
} from '@/react/docker/models/types';
import type { EnvironmentId } from '@/react/portainer/environments/types';

import { ButtonGroup, Button, AddButton } from '@@/buttons';

interface Props {
  selectedItems: ModelListViewModel[];
  isAddActionVisible: boolean;
  endpointId: EnvironmentId;
}

export function ModelsDatatableActions({
  selectedItems,
  isAddActionVisible,
  endpointId,
}: Props) {
  // Mock state for model runner status - this should eventually come from docker model status API
  const [isRunnerRunning, setIsRunnerRunning] = useState(true);

  const selectedItemCount = selectedItems.length;
  const hasRunningItemsSelected = selectedItems.some((item) =>
    item.Status === ModelStatus.Running
  );
  const hasStoppedItemsSelected = selectedItems.some((item) =>
    item.Status === ModelStatus.Stopped
  );

  const { authorized } = useAuthorizations([
    'DockerContainerStart',
    'DockerContainerStop',
    'DockerContainerRestart',
    'DockerContainerDelete',
    'DockerContainerCreate',
  ]);

  if (!authorized) {
    return null;
  }

  return (
    <div className="flex gap-2">
      {/* Model Runner Status Widget */}
      <ButtonGroup>
        <Authorized authorizations="DockerContainerStart">
          <Button
            color="light"
            data-cy="model-runner-status-button"
            icon={isRunnerRunning ? UpToDate : UpdatesUnknown}
            disabled
            className={isRunnerRunning ? 'brightness-110 contrast-125' : ''}
          >
            {isRunnerRunning ? 'Model runner is running' : 'Model runner is not running'}
          </Button>
        </Authorized>

        <Authorized authorizations={isRunnerRunning ? 'DockerContainerDelete' : 'DockerContainerCreate'}>
          <Button
            color="light"
            data-cy={isRunnerRunning ? 'uninstall-model-runner-button' : 'install-model-runner-button'}
            onClick={() => handleRunnerAction()}
            icon={isRunnerRunning ? Upload : Download}
            className={isRunnerRunning ? 'opacity-50 hover:opacity-100 transition-opacity' : ''}
          >
            {isRunnerRunning ? 'Uninstall Model Runner' : 'Install Model Runner'}
          </Button>
        </Authorized>
      </ButtonGroup>

      {/* Model Action Buttons */}
      <ButtonGroup>
        <Authorized authorizations="DockerContainerStop">
          <Button
            color="light"
            data-cy="stop-docker-model-button"
            onClick={() => onStopClick(selectedItems)}
            disabled={selectedItemCount === 0 || !hasRunningItemsSelected}
            icon={Square}
          >
            Stop
          </Button>
        </Authorized>

        <Authorized authorizations="DockerContainerRestart">
          <Button
            color="light"
            data-cy="restart-docker-model-button"
            onClick={() => onRestartClick(selectedItems)}
            disabled={selectedItemCount === 0 || hasStoppedItemsSelected}
            icon={RefreshCw}
          >
            Restart
          </Button>
        </Authorized>

        <Authorized authorizations="DockerContainerDelete">
          <Button
            color="dangerlight"
            data-cy="remove-docker-model-button"
            onClick={() => onRemoveClick(selectedItems)}
            disabled={selectedItemCount === 0}
            icon={Trash2}
          >
            Remove
          </Button>
        </Authorized>
      </ButtonGroup>
      {isAddActionVisible && (
        <div className="space-left">
          <Authorized authorizations="DockerContainerCreate">
            <AddButton data-cy="pull-docker-model-button">
              Add model
            </AddButton>
          </Authorized>
        </div>
      )}
    </div>
  );

  function onStopClick(selectedItems: ModelListViewModel[]) {
    // TODO: Implement docker model unload functionality
    // eslint-disable-next-line no-console
    console.log('Stop models:', selectedItems.map(m => m.ModelName));
  }

  function onRestartClick(selectedItems: ModelListViewModel[]) {
    // TODO: Implement docker model restart (unload + run) functionality
    // eslint-disable-next-line no-console
    console.log('Restart models:', selectedItems.map(m => m.ModelName));
  }

  function onRemoveClick(selectedItems: ModelListViewModel[]) {
    // TODO: Implement docker model rm functionality
    // eslint-disable-next-line no-console
    console.log('Remove models:', selectedItems.map(m => m.ModelName));
  }

  function handleRunnerAction() {
    if (isRunnerRunning) {
      // TODO: Implement docker model uninstall-runner functionality
      // eslint-disable-next-line no-console
      console.log('Uninstall model runner for environment:', endpointId);
      setIsRunnerRunning(false); // Mock state change
    } else {
      // TODO: Implement docker model install-runner functionality
      // eslint-disable-next-line no-console
      console.log('Install model runner for environment:', endpointId);
      setIsRunnerRunning(true); // Mock state change
    }
  }
}
