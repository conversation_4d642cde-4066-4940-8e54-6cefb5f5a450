package http

import (
	"context"
	"crypto/tls"
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"time"

	portaineree "github.com/portainer/portainer-ee/api"
	"github.com/portainer/portainer-ee/api/adminmonitor"
	"github.com/portainer/portainer-ee/api/apikey"
	backupOps "github.com/portainer/portainer-ee/api/backup"
	"github.com/portainer/portainer-ee/api/cloud"
	"github.com/portainer/portainer-ee/api/dataservices"
	"github.com/portainer/portainer-ee/api/dataservices/cloudcredential"
	"github.com/portainer/portainer-ee/api/docker"
	"github.com/portainer/portainer-ee/api/docker/client"
	"github.com/portainer/portainer-ee/api/http/handler"
	"github.com/portainer/portainer-ee/api/http/handler/auth"
	"github.com/portainer/portainer-ee/api/http/handler/backup"
	"github.com/portainer/portainer-ee/api/http/handler/cloudcredentials"
	"github.com/portainer/portainer-ee/api/http/handler/customtemplates"
	dockerhandler "github.com/portainer/portainer-ee/api/http/handler/docker"
	"github.com/portainer/portainer-ee/api/http/handler/edgeconfigs"
	"github.com/portainer/portainer-ee/api/http/handler/edgegroups"
	"github.com/portainer/portainer-ee/api/http/handler/edgejobs"
	"github.com/portainer/portainer-ee/api/http/handler/edgestacks"
	"github.com/portainer/portainer-ee/api/http/handler/edgeupdateschedules"
	"github.com/portainer/portainer-ee/api/http/handler/endpointedge"
	"github.com/portainer/portainer-ee/api/http/handler/endpointgroups"
	"github.com/portainer/portainer-ee/api/http/handler/endpointproxy"
	"github.com/portainer/portainer-ee/api/http/handler/endpoints"
	"github.com/portainer/portainer-ee/api/http/handler/file"
	"github.com/portainer/portainer-ee/api/http/handler/gitops"
	"github.com/portainer/portainer-ee/api/http/handler/helm"
	"github.com/portainer/portainer-ee/api/http/handler/hostmanagement/openamt"
	"github.com/portainer/portainer-ee/api/http/handler/kaas"
	kubehandler "github.com/portainer/portainer-ee/api/http/handler/kubernetes"
	"github.com/portainer/portainer-ee/api/http/handler/ldap"
	"github.com/portainer/portainer-ee/api/http/handler/licenses"
	"github.com/portainer/portainer-ee/api/http/handler/metrics"
	"github.com/portainer/portainer-ee/api/http/handler/motd"
	observabilityHandler "github.com/portainer/portainer-ee/api/http/handler/observability"
	"github.com/portainer/portainer-ee/api/http/handler/omni"
	"github.com/portainer/portainer-ee/api/http/handler/registries"
	"github.com/portainer/portainer-ee/api/http/handler/resourcecontrols"
	"github.com/portainer/portainer-ee/api/http/handler/roles"
	"github.com/portainer/portainer-ee/api/http/handler/settings"
	"github.com/portainer/portainer-ee/api/http/handler/sshkey"
	sslhandler "github.com/portainer/portainer-ee/api/http/handler/ssl"
	"github.com/portainer/portainer-ee/api/http/handler/stacks"
	"github.com/portainer/portainer-ee/api/http/handler/storybook"
	"github.com/portainer/portainer-ee/api/http/handler/support"
	"github.com/portainer/portainer-ee/api/http/handler/system"
	"github.com/portainer/portainer-ee/api/http/handler/tags"
	"github.com/portainer/portainer-ee/api/http/handler/teammemberships"
	"github.com/portainer/portainer-ee/api/http/handler/teams"
	"github.com/portainer/portainer-ee/api/http/handler/templates"
	"github.com/portainer/portainer-ee/api/http/handler/upload"
	"github.com/portainer/portainer-ee/api/http/handler/useractivity"
	"github.com/portainer/portainer-ee/api/http/handler/users"
	"github.com/portainer/portainer-ee/api/http/handler/webhooks"
	"github.com/portainer/portainer-ee/api/http/handler/websocket"
	"github.com/portainer/portainer-ee/api/http/proxy"
	"github.com/portainer/portainer-ee/api/http/proxy/factory/kubernetes"
	"github.com/portainer/portainer-ee/api/http/security"
	alertmanagerServer "github.com/portainer/portainer-ee/api/internal/alertmanager/server"
	"github.com/portainer/portainer-ee/api/internal/authorization"
	"github.com/portainer/portainer-ee/api/internal/diagnostics"
	"github.com/portainer/portainer-ee/api/internal/edge/edgeasync"
	edgestackservice "github.com/portainer/portainer-ee/api/internal/edge/edgestacks"
	"github.com/portainer/portainer-ee/api/internal/edge/staggers"
	"github.com/portainer/portainer-ee/api/internal/edge/updateschedules"
	internalmetrics "github.com/portainer/portainer-ee/api/internal/metrics"
	"github.com/portainer/portainer-ee/api/internal/snapshot"
	"github.com/portainer/portainer-ee/api/internal/ssl"
	"github.com/portainer/portainer-ee/api/internal/update"
	"github.com/portainer/portainer-ee/api/internal/update/autopatch"
	k8s "github.com/portainer/portainer-ee/api/kubernetes"
	"github.com/portainer/portainer-ee/api/kubernetes/cli"
	"github.com/portainer/portainer-ee/api/observability/alerting"
	"github.com/portainer/portainer-ee/api/pendingactions"
	"github.com/portainer/portainer-ee/api/platform"
	"github.com/portainer/portainer-ee/api/scheduler"
	"github.com/portainer/portainer-ee/api/stacks/deployments"
	portainer "github.com/portainer/portainer/api"
	"github.com/portainer/portainer/api/crypto"
	cehttp "github.com/portainer/portainer/api/http"
	"github.com/portainer/portainer/api/http/csrf"
	"github.com/portainer/portainer/api/http/middlewares"
	"github.com/portainer/portainer/api/http/offlinegate"
	securityce "github.com/portainer/portainer/api/http/security"
	"github.com/portainer/portainer/pkg/fips"
	libhelmtypes "github.com/portainer/portainer/pkg/libhelm/types"

	"github.com/pkg/errors"
	"github.com/rs/zerolog/log"
)

// Server implements the portaineree.Server interface
type Server struct {
	AuthorizationService        *authorization.Service
	BindAddress                 string
	BindAddressHTTPS            string
	CSP                         bool
	HTTPEnabled                 bool
	AssetsPath                  string
	Status                      *portainer.Status
	ReverseTunnelService        portaineree.ReverseTunnelService
	ComposeStackManager         portaineree.ComposeStackManager
	CryptoService               portainer.CryptoService
	LicenseService              portaineree.LicenseService
	SignatureService            portainer.DigitalSignatureService
	EdgeAsyncService            *edgeasync.Service
	EdgeStacksService           *edgestackservice.Service
	EdgeStaggerService          staggers.StaggerService
	SnapshotService             portaineree.SnapshotService
	FileService                 portaineree.FileService
	DataStore                   dataservices.DataStore
	GitService                  portainer.GitService
	APIKeyService               apikey.APIKeyService
	OpenAMTService              portainer.OpenAMTService // will be nil if FIPS mode is enabled
	AlertingService             *alerting.Service
	JWTService                  portainer.JWTService
	LDAPService                 portaineree.LDAPService
	OAuthService                portaineree.OAuthService
	SwarmStackManager           portaineree.SwarmStackManager
	UserActivityStore           portaineree.UserActivityStore
	UserActivityService         portaineree.UserActivityService
	ProxyManager                *proxy.Manager
	KubernetesTokenCacheManager *kubernetes.TokenCacheManager
	KubeClusterAccessService    k8s.KubeClusterAccessService
	Handler                     *handler.Handler
	SSLService                  *ssl.Service
	DockerClientFactory         *client.ClientFactory
	KubernetesClientFactory     *cli.ClientFactory
	KubernetesDeployer          portaineree.KubernetesDeployer
	HelmPackageManager          libhelmtypes.HelmPackageManager
	Scheduler                   *scheduler.Scheduler
	ShutdownCtx                 context.Context
	ShutdownTrigger             context.CancelFunc
	CloudManagementService      *cloud.CloudManagementService // will be nil if FIPS mode is enabled
	CloudInfoService            *cloud.CloudInfoService       // will be nil if FIPS mode is enabled
	CloudCredentialService      *cloudcredential.Service
	StackDeployer               deployments.StackDeployer
	UpdateService               update.Service
	AdminCreationDone           chan struct{}
	PendingActionsService       *pendingactions.PendingActionsService
	MetricsService              *internalmetrics.Service
	AutoPatchService            autopatch.Service
	PlatformService             platform.Service
	OmniService                 portaineree.OmniService
	PullLimitCheckDisabled      bool
	TrustedOrigins              []string
}

// Start starts the HTTP server
func (server *Server) Start() error {
	server.AuthorizationService.RegisterEventHandler("kubernetesTokenCacheManager", server.KubernetesTokenCacheManager)

	requestBouncer := security.NewRequestBouncer(server.DataStore, server.LicenseService, server.JWTService, server.APIKeyService, server.SSLService)
	if !server.CSP {
		requestBouncer.DisableCSP()
	}

	rateLimiter := securityce.NewRateLimiter(10, 1*time.Second, 1*time.Hour)
	offlineGate := offlinegate.NewOfflineGate()

	passwordStrengthChecker := security.NewPasswordStrengthChecker(server.DataStore.Settings())

	var authHandler = auth.NewHandler(requestBouncer, rateLimiter, passwordStrengthChecker, server.KubernetesClientFactory)
	authHandler.AuthorizationService = server.AuthorizationService
	authHandler.APIKeyService = server.APIKeyService
	authHandler.DataStore = server.DataStore
	authHandler.CryptoService = server.CryptoService
	authHandler.JWTService = server.JWTService
	authHandler.LDAPService = server.LDAPService
	authHandler.LicenseService = server.LicenseService
	authHandler.ProxyManager = server.ProxyManager
	authHandler.KubernetesTokenCacheManager = server.KubernetesTokenCacheManager
	authHandler.OAuthService = server.OAuthService
	authHandler.UserActivityService = server.UserActivityService
	authHandler.MetricsService = server.MetricsService

	adminMonitor := adminmonitor.New(5*time.Minute, server.DataStore, server.ShutdownCtx)
	adminMonitor.Start()

	backupScheduler := backupOps.NewBackupScheduler(offlineGate, server.DataStore, server.UserActivityStore, server.FileService.GetDatastorePath(), server.MetricsService)
	if err := backupScheduler.Start(); err != nil {
		return errors.Wrap(err, "failed to start backup scheduler")
	}
	var backupHandler = backup.NewHandler(
		requestBouncer,
		server.DataStore,
		server.UserActivityStore,
		offlineGate,
		server.FileService.GetDatastorePath(),
		backupScheduler,
		server.ShutdownTrigger,
		adminMonitor,
		server.MetricsService,
	)

	var roleHandler = roles.NewHandler(requestBouncer)
	roleHandler.DataStore = server.DataStore

	var customTemplatesHandler = customtemplates.NewHandler(requestBouncer, server.DataStore, server.FileService, server.GitService, server.UserActivityService)

	var edgeConfigHandler = edgeconfigs.NewHandler(server.DataStore, requestBouncer, server.UserActivityService, server.EdgeAsyncService, server.FileService)

	var edgeGroupsHandler = edgegroups.NewHandler(requestBouncer, server.UserActivityService, server.EdgeAsyncService)
	edgeGroupsHandler.DataStore = server.DataStore
	edgeGroupsHandler.ReverseTunnelService = server.ReverseTunnelService
	edgeGroupsHandler.FileService = server.FileService

	var edgeJobsHandler = edgejobs.NewHandler(requestBouncer, server.UserActivityService, server.EdgeAsyncService)
	edgeJobsHandler.DataStore = server.DataStore
	edgeJobsHandler.FileService = server.FileService
	edgeJobsHandler.ReverseTunnelService = server.ReverseTunnelService

	edgeUpdateService, err := updateschedules.NewService(server.DataStore, server.AssetsPath, server.EdgeStacksService, server.FileService)
	if err != nil {
		return errors.WithMessage(err, "Unable to create EdgeUpdateService")
	}

	edgeUpdateScheduleHandler := edgeupdateschedules.NewHandler(requestBouncer, server.DataStore, server.FileService, server.AssetsPath, server.EdgeStacksService, edgeUpdateService)
	edgeUpdateScheduleHandler.ReverseTunnelService = server.ReverseTunnelService

	var edgeStacksHandler = edgestacks.NewHandler(requestBouncer,
		server.UserActivityService,
		server.DataStore,
		server.EdgeAsyncService,
		server.EdgeStacksService,
		edgeUpdateService,
		server.Scheduler,
		server.EdgeStaggerService,
	)
	edgeStacksHandler.FileService = server.FileService
	edgeStacksHandler.GitService = server.GitService
	edgeStacksHandler.KubernetesDeployer = server.KubernetesDeployer

	if err := edgeStacksHandler.StartAutoUpdateSchedules(server.Scheduler); err != nil {
		return fmt.Errorf("failed to start auto update schedules: %w", err)
	}

	var endpointHandler = endpoints.NewHandler(requestBouncer, server.UserActivityService, server.DataStore, server.EdgeAsyncService, server.CloudManagementService, server.LicenseService)
	endpointHandler.AuthorizationService = server.AuthorizationService
	endpointHandler.FileService = server.FileService
	endpointHandler.ProxyManager = server.ProxyManager
	endpointHandler.SnapshotService = server.SnapshotService
	endpointHandler.ReverseTunnelService = server.ReverseTunnelService
	endpointHandler.K8sClientFactory = server.KubernetesClientFactory
	endpointHandler.ComposeStackManager = server.ComposeStackManager
	endpointHandler.DockerClientFactory = server.DockerClientFactory
	endpointHandler.BindAddress = server.BindAddress
	endpointHandler.BindAddressHTTPS = server.BindAddressHTTPS
	endpointHandler.KubernetesTokenCacheManager = server.KubernetesTokenCacheManager
	endpointHandler.KubernetesDeployer = server.KubernetesDeployer
	endpointHandler.AssetsPath = server.AssetsPath
	endpointHandler.PendingActionsService = server.PendingActionsService
	endpointHandler.PullLimitCheckDisabled = server.PullLimitCheckDisabled
	endpointHandler.MetricsService = server.MetricsService

	var endpointEdgeHandler = endpointedge.NewHandler(requestBouncer,
		server.DataStore,
		server.FileService,
		server.ReverseTunnelService,
		server.EdgeAsyncService,
		server.LicenseService,
		edgeUpdateService,
		server.EdgeStaggerService)

	var endpointGroupHandler = endpointgroups.NewHandler(requestBouncer, server.UserActivityService, server.EdgeAsyncService, server.FileService)
	endpointGroupHandler.AuthorizationService = server.AuthorizationService
	endpointGroupHandler.DataStore = server.DataStore
	endpointGroupHandler.PendingActionsService = server.PendingActionsService

	var endpointProxyHandler = endpointproxy.NewHandler(requestBouncer)
	endpointProxyHandler.DataStore = server.DataStore
	endpointProxyHandler.ProxyManager = server.ProxyManager
	endpointProxyHandler.ReverseTunnelService = server.ReverseTunnelService

	var kaasHandler *kaas.Handler
	if !fips.FIPSMode() {
		kaasHandler = kaas.NewHandler(requestBouncer, server.DataStore, server.CloudManagementService, server.CloudInfoService, server.UserActivityService, server.LicenseService)
	}

	var kubernetesHandler = kubehandler.NewHandler(requestBouncer, server.AuthorizationService, server.DataStore, server.JWTService, server.KubeClusterAccessService, server.KubernetesClientFactory, nil, server.UserActivityService, server.KubernetesDeployer, server.FileService, server.AssetsPath)

	containerService := docker.NewContainerService(server.DockerClientFactory, server.DataStore)

	modelService := docker.NewModelService(server.FileService.GetBinaryFolder(), server.FileService.GetDockerConfigPath(), server.ReverseTunnelService)

	var dockerHandler = dockerhandler.NewHandler(requestBouncer, server.AuthorizationService, server.DataStore, server.DockerClientFactory, containerService, modelService)

	var licenseHandler = licenses.NewHandler(requestBouncer, server.UserActivityService)
	licenseHandler.LicenseService = server.LicenseService

	var fileHandler = file.NewHandler(filepath.Join(server.AssetsPath, "public"), server.CSP, adminMonitor.WasInstanceDisabled)

	var endpointHelmHandler = helm.NewHandler(requestBouncer, server.DataStore, server.JWTService, server.KubernetesDeployer, server.HelmPackageManager, server.KubeClusterAccessService, server.UserActivityService, server.FileService)
	endpointHelmHandler.K8sClientFactory = server.KubernetesClientFactory

	var gitOperationHandler = gitops.NewHandler(requestBouncer, server.DataStore, server.GitService, server.FileService)

	var helmTemplatesHandler = helm.NewTemplateHandler(requestBouncer, server.DataStore, server.HelmPackageManager, server.FileService)
	helmTemplatesHandler.K8sClientFactory = server.KubernetesClientFactory

	var ldapHandler = ldap.NewHandler(requestBouncer)
	ldapHandler.DataStore = server.DataStore
	ldapHandler.FileService = server.FileService
	ldapHandler.LDAPService = server.LDAPService

	var motdHandler = motd.NewHandler(requestBouncer)

	var registryHandler = registries.NewHandler(requestBouncer, server.UserActivityService)
	registryHandler.DataStore = server.DataStore
	registryHandler.FileService = server.FileService
	registryHandler.ProxyManager = server.ProxyManager
	registryHandler.K8sClientFactory = server.KubernetesClientFactory
	registryHandler.PendingActionsService = server.PendingActionsService

	var resourceControlHandler = resourcecontrols.NewHandler(requestBouncer, server.DataStore, server.UserActivityService)

	var settingsHandler = settings.NewHandler(requestBouncer, server.UserActivityService)
	settingsHandler.AuthorizationService = server.AuthorizationService
	settingsHandler.DataStore = server.DataStore
	settingsHandler.FileService = server.FileService
	settingsHandler.JWTService = server.JWTService
	settingsHandler.LDAPService = server.LDAPService
	settingsHandler.SnapshotService = server.SnapshotService
	settingsHandler.SSLService = server.SSLService
	settingsHandler.MetricsService = server.MetricsService
	settingsHandler.AutoPatchService = server.AutoPatchService

	var sslHandler = sslhandler.NewHandler(requestBouncer)
	sslHandler.SSLService = server.SSLService

	var openAMTHandler *openamt.Handler
	if !fips.FIPSMode() {
		openAMTHandler = openamt.NewHandler(requestBouncer, server.DataStore)
		openAMTHandler.OpenAMTService = server.OpenAMTService
		openAMTHandler.DataStore = server.DataStore
		openAMTHandler.DockerClientFactory = server.DockerClientFactory
	}

	var stackHandler = stacks.NewHandler(requestBouncer, server.DataStore, server.UserActivityService)
	stackHandler.DockerClientFactory = server.DockerClientFactory
	stackHandler.FileService = server.FileService
	stackHandler.KubernetesDeployer = server.KubernetesDeployer
	stackHandler.GitService = server.GitService
	stackHandler.KubernetesClientFactory = server.KubernetesClientFactory
	stackHandler.AuthorizationService = server.AuthorizationService
	stackHandler.Scheduler = server.Scheduler
	stackHandler.SwarmStackManager = server.SwarmStackManager
	stackHandler.ComposeStackManager = server.ComposeStackManager
	stackHandler.StackDeployer = server.StackDeployer

	var systemHandler = system.NewHandler(requestBouncer, server.Status, server.DataStore, server.PlatformService, server.UpdateService)

	var storybookHandler = storybook.NewHandler(server.AssetsPath)

	var tagHandler = tags.NewHandler(requestBouncer, server.UserActivityService, server.EdgeAsyncService, server.FileService)
	tagHandler.DataStore = server.DataStore

	var teamHandler = teams.NewHandler(requestBouncer, server.UserActivityService)
	teamHandler.AuthorizationService = server.AuthorizationService
	teamHandler.DataStore = server.DataStore
	teamHandler.K8sClientFactory = server.KubernetesClientFactory

	var teamMembershipHandler = teammemberships.NewHandler(requestBouncer, server.UserActivityService)
	teamMembershipHandler.AuthorizationService = server.AuthorizationService
	teamMembershipHandler.DataStore = server.DataStore
	teamMembershipHandler.K8sClientFactory = server.KubernetesClientFactory

	var templatesHandler = templates.NewHandler(requestBouncer)
	templatesHandler.DataStore = server.DataStore
	templatesHandler.FileService = server.FileService
	templatesHandler.GitService = server.GitService

	var uploadHandler = upload.NewHandler(requestBouncer, server.UserActivityService)
	uploadHandler.FileService = server.FileService

	var userHandler = users.NewHandler(requestBouncer, rateLimiter, server.APIKeyService, server.UserActivityService, passwordStrengthChecker)
	userHandler.AuthorizationService = server.AuthorizationService
	userHandler.DataStore = server.DataStore
	userHandler.CryptoService = server.CryptoService
	userHandler.K8sClientFactory = server.KubernetesClientFactory
	userHandler.AdminCreationDone = server.AdminCreationDone
	userHandler.FileService = server.FileService
	userHandler.PendingActionsService = server.PendingActionsService

	var userActivityHandler = useractivity.NewHandler(requestBouncer)
	userActivityHandler.UserActivityStore = server.UserActivityStore

	var websocketHandler = websocket.NewHandler(server.KubernetesTokenCacheManager, requestBouncer, server.AuthorizationService, server.DataStore, server.UserActivityService)
	websocketHandler.SignatureService = server.SignatureService
	websocketHandler.ReverseTunnelService = server.ReverseTunnelService
	websocketHandler.KubernetesClientFactory = server.KubernetesClientFactory

	var webhookHandler = webhooks.NewHandler(requestBouncer, server.DataStore, server.UserActivityService, containerService)
	webhookHandler.DockerClientFactory = server.DockerClientFactory

	var cloudCredHandler = cloudcredentials.NewHandler(requestBouncer, server.UserActivityService, server.OmniService)
	cloudCredHandler.DataStore = server.DataStore

	var sshKeyHandler = sshkey.NewHandler(requestBouncer, server.UserActivityService)

	var observabilityHandler = observabilityHandler.NewHandler(requestBouncer, server.DataStore)
	observabilityHandler.AlertingService = server.AlertingService

	internalAlertManager, err := alertmanagerServer.NewService()
	if err != nil {
		return errors.Wrap(err, "failed to create internal alert manager service")
	}
	observabilityHandler.InternalAlertManager = internalAlertManager

	var omnihandler *omni.Handler
	if !fips.FIPSMode() {
		omnihandler = omni.NewHandler(requestBouncer, server.DataStore, server.OmniService, server.ReverseTunnelService)
	}

	var supportHandler = support.NewHandler(requestBouncer, server.DataStore, server.UserActivityStore, server.FileService.GetDatastorePath())

	var metricsHandler = metrics.NewHandler(requestBouncer, server.UserActivityService)

	// undocumented / hidden flag to enable pprof endpoints
	var pprofHandler *diagnostics.PProfHandler
	enablePProf := os.Getenv("DEBUG_ENABLE_PPROF")
	if enablePProf != "" {
		pprofHandler = diagnostics.NewHandler(enablePProf)
		path := fmt.Sprintf("/%s/debug/pprof/list", pprofHandler.Path)
		log.Info().
			Str("path", path).
			Msg("pprof endpoints enabled, go to your Portainer http(s) address at " + path)
	}

	server.Handler = &handler.Handler{
		RoleHandler:               roleHandler,
		AuthHandler:               authHandler,
		BackupHandler:             backupHandler,
		CustomTemplatesHandler:    customTemplatesHandler,
		DockerHandler:             dockerHandler,
		EdgeConfigHandler:         edgeConfigHandler,
		EdgeGroupsHandler:         edgeGroupsHandler,
		EdgeJobsHandler:           edgeJobsHandler,
		EdgeUpdateScheduleHandler: edgeUpdateScheduleHandler,
		EdgeStacksHandler:         edgeStacksHandler,
		EndpointGroupHandler:      endpointGroupHandler,
		EndpointHandler:           endpointHandler,
		EndpointHelmHandler:       endpointHelmHandler,
		EndpointEdgeHandler:       endpointEdgeHandler,
		EndpointProxyHandler:      endpointProxyHandler,
		GitOperationHandler:       gitOperationHandler,
		HelmTemplatesHandler:      helmTemplatesHandler,
		KaasHandler:               kaasHandler,
		KubernetesHandler:         kubernetesHandler,
		FileHandler:               fileHandler,
		LDAPHandler:               ldapHandler,
		LicenseHandler:            licenseHandler,
		MetricsHandler:            metricsHandler,
		MOTDHandler:               motdHandler,
		OpenAMTHandler:            openAMTHandler,
		ObservabilityHandler:      observabilityHandler,
		OmniHandler:               omnihandler,
		RegistryHandler:           registryHandler,
		ResourceControlHandler:    resourceControlHandler,
		SettingsHandler:           settingsHandler,
		SSLHandler:                sslHandler,
		SystemHandler:             systemHandler,
		StackHandler:              stackHandler,
		StorybookHandler:          storybookHandler,
		TagHandler:                tagHandler,
		TeamHandler:               teamHandler,
		TeamMembershipHandler:     teamMembershipHandler,
		TemplatesHandler:          templatesHandler,
		UploadHandler:             uploadHandler,
		UserHandler:               userHandler,
		UserActivityHandler:       userActivityHandler,
		WebSocketHandler:          websocketHandler,
		WebhookHandler:            webhookHandler,
		CloudCredentialsHandler:   cloudCredHandler,
		SSHKeyHandler:             sshKeyHandler,
		SupportHandler:            supportHandler,
		PProfHandler:              pprofHandler,
	}

	errorLogger := cehttp.NewHTTPLogger()

	handler := adminMonitor.WithRedirect(offlineGate.WaitingMiddleware(time.Minute, server.Handler))

	handler = middlewares.WithPanicLogger(middlewares.WithSlowRequestsLogger(handler))

	handler, err = csrf.WithProtect(handler, server.TrustedOrigins)
	if err != nil {
		return errors.Wrap(err, "failed to create CSRF middleware")
	}

	if server.HTTPEnabled {
		go func() {
			log.Info().Str("bind_address", server.BindAddress).Msg("starting HTTP server")
			httpServer := &http.Server{
				Addr:     server.BindAddress,
				Handler:  middlewares.PlaintextHTTPRequest(handler),
				ErrorLog: errorLogger,
			}

			go shutdown(server.ShutdownCtx, httpServer, backupScheduler)

			err := httpServer.ListenAndServe()
			if err != nil && err != http.ErrServerClosed {
				log.Error().Err(err).Msg("HTTP server failed to start")
			}
		}()
	}

	log.Info().Str("bind_address", server.BindAddressHTTPS).Msg("starting HTTPS server")
	httpsServer := &http.Server{
		Addr:         server.BindAddressHTTPS,
		Handler:      handler,
		ErrorLog:     errorLogger,
		TLSNextProto: make(map[string]func(*http.Server, *tls.Conn, http.Handler)), // Disable HTTP/2
	}

	httpsServer.TLSConfig = crypto.CreateTLSConfiguration(false)
	httpsServer.TLSConfig.GetCertificate = func(chi *tls.ClientHelloInfo) (*tls.Certificate, error) {
		if chi.ServerName == "" {
			return server.SSLService.GetRawCertificate(), nil
		}

		if mTLSCert := server.SSLService.GetRawMTLSCertificate(); mTLSCert != nil {
			if err := mTLSCert.Leaf.VerifyHostname(chi.ServerName); err == nil {
				return mTLSCert, nil
			}
		}

		return server.SSLService.GetRawCertificate(), nil
	}

	caCertPool := server.SSLService.GetCACertificatePool()
	if caCertPool != nil {
		log.Debug().Str("bind_address_HTTPS", server.BindAddressHTTPS).Msg("using CA certificate")

		httpsServer.TLSConfig.ClientCAs = caCertPool
		httpsServer.TLSConfig.ClientAuth = tls.VerifyClientCertIfGiven // can't use tls.RequireAndVerifyClientCert, this port is also used for the browser
	}

	go shutdown(server.ShutdownCtx, httpsServer, backupScheduler)
	go snapshot.NewBackgroundSnapshotter(server.DataStore, server.ReverseTunnelService)

	return httpsServer.ListenAndServeTLS("", "")
}

func shutdown(shutdownCtx context.Context, httpServer *http.Server, backupScheduler *backupOps.BackupScheduler) {
	<-shutdownCtx.Done()

	backupScheduler.Stop()

	log.Debug().Msg("shutting down the HTTP server")
	shutdownTimeout, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	err := httpServer.Shutdown(shutdownTimeout)
	if err != nil {
		log.Error().
			Err(err).
			Msg("failed to shut down the HTTP server")
	}
}
