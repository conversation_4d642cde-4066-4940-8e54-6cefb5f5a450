package docker

import (
	"context"
	"testing"
	"time"

	portainer "github.com/portainer/portainer/api"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockReverseTunnelService is a mock implementation of ReverseTunnelService
type MockReverseTunnelService struct {
	mock.Mock
}

func (m *MockReverseTunnelService) TunnelAddr(endpoint *portainer.Endpoint) (string, error) {
	args := m.Called(endpoint)
	return args.String(0), args.Error(1)
}

func (m *MockReverseTunnelService) Config(endpointID portainer.EndpointID) portainer.TunnelDetails {
	args := m.Called(endpointID)
	return args.Get(0).(portainer.TunnelDetails)
}

func (m *MockReverseTunnelService) StartTunnelServer(addr, port string, snapshotService portainer.SnapshotService) error {
	args := m.Called(addr, port, snapshotService)
	return args.Error(0)
}

func (m *MockReverseTunnelService) StopTunnelServer() error {
	args := m.Called()
	return args.Error(0)
}

func (m *MockReverseTunnelService) GenerateEdgeKey(apiURL, tunnelAddr string, endpointIdentifier int) string {
	args := m.Called(apiURL, tunnelAddr, endpointIdentifier)
	return args.String(0)
}

func (m *MockReverseTunnelService) Open(endpoint *portainer.Endpoint) error {
	args := m.Called(endpoint)
	return args.Error(0)
}

func (m *MockReverseTunnelService) UpdateLastActivity(endpointID portainer.EndpointID) {
	m.Called(endpointID)
}

func (m *MockReverseTunnelService) KeepTunnelAlive(endpointID portainer.EndpointID, ctx context.Context, maxKeepAlive time.Duration) {
	m.Called(endpointID, ctx, maxKeepAlive)
}

func TestNewModelService(t *testing.T) {
	binaryPath := "/usr/bin"
	configPath := "/etc/docker"
	mockReverseTunnelService := &MockReverseTunnelService{}

	service := NewModelService(binaryPath, configPath, mockReverseTunnelService)

	assert.NotNil(t, service)
	assert.Equal(t, binaryPath, service.binaryPath)
	assert.Equal(t, configPath, service.configPath)
	assert.Equal(t, mockReverseTunnelService, service.reverseTunnelService)
}

func TestPrepareDockerCommandAndArgs(t *testing.T) {
	tests := []struct {
		name         string
		endpoint     *portainer.Endpoint
		expectedArgs []string
		expectError  bool
	}{
		{
			name: "Local Docker endpoint",
			endpoint: &portainer.Endpoint{
				URL: "unix:///var/run/docker.sock",
				TLSConfig: portainer.TLSConfiguration{
					TLS: false,
				},
			},
			expectedArgs: []string{"--config", "/etc/docker"},
			expectError:  false,
		},
		{
			name: "Remote Docker endpoint with TLS",
			endpoint: &portainer.Endpoint{
				URL: "tcp://*************:2376",
				TLSConfig: portainer.TLSConfiguration{
					TLS:           true,
					TLSSkipVerify: false,
				},
			},
			expectedArgs: []string{"--config", "/etc/docker", "--host", "tcp://*************:2376", "--tls", "--tlsverify"},
			expectError:  false,
		},
		{
			name: "Remote Docker endpoint with TLS skip verify",
			endpoint: &portainer.Endpoint{
				URL: "tcp://*************:2376",
				TLSConfig: portainer.TLSConfiguration{
					TLS:           true,
					TLSSkipVerify: true,
				},
			},
			expectedArgs: []string{"--config", "/etc/docker", "--host", "tcp://*************:2376", "--tls"},
			expectError:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockReverseTunnelService := &MockReverseTunnelService{}
			service := NewModelService("/usr/bin", "/etc/docker", mockReverseTunnelService)

			command, args, err := service.prepareDockerCommandAndArgs(tt.endpoint)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Contains(t, command, "docker")
				assert.Equal(t, tt.expectedArgs, args)
			}
		})
	}
}

func TestPrepareDockerCommandAndArgsEdgeAgent(t *testing.T) {
	mockReverseTunnelService := &MockReverseTunnelService{}
	mockReverseTunnelService.On("TunnelAddr", mock.Anything).Return("127.0.0.1:8080", nil)

	service := NewModelService("/usr/bin", "/etc/docker", mockReverseTunnelService)

	endpoint := &portainer.Endpoint{
		Type: portainer.EdgeAgentOnDockerEnvironment,
		URL:  "tcp://edge-agent:2375",
		TLSConfig: portainer.TLSConfiguration{
			TLS: false,
		},
	}

	command, args, err := service.prepareDockerCommandAndArgs(endpoint)

	assert.NoError(t, err)
	assert.Contains(t, command, "docker")
	expectedArgs := []string{"--config", "/etc/docker", "--host", "tcp://127.0.0.1:8080"}
	assert.Equal(t, expectedArgs, args)

	mockReverseTunnelService.AssertExpectations(t)
}

func TestModelListItemStruct(t *testing.T) {
	now := time.Now()
	model := ModelListItem{
		ModelName:     "test-model",
		Parameters:    "1B",
		Quantization:  "Q4_0",
		Architecture:  "llama",
		ModelID:       "abc123",
		Created:       now,
		Size:          "1.5GB",
	}

	assert.Equal(t, "test-model", model.ModelName)
	assert.Equal(t, "1B", model.Parameters)
	assert.Equal(t, "Q4_0", model.Quantization)
	assert.Equal(t, "llama", model.Architecture)
	assert.Equal(t, "abc123", model.ModelID)
	assert.Equal(t, now, model.Created)
	assert.Equal(t, "1.5GB", model.Size)
}

func TestModelRunningItemStruct(t *testing.T) {
	model := ModelRunningItem{
		ModelName: "running-model",
		ModelID:   "def456",
		Status:    "running",
	}

	assert.Equal(t, "running-model", model.ModelName)
	assert.Equal(t, "def456", model.ModelID)
	assert.Equal(t, "running", model.Status)
}

func TestModelInspectResultStruct(t *testing.T) {
	result := ModelInspectResult{
		ID:      "ghi789",
		Tags:    "latest",
		Created: "2023-01-01T00:00:00Z",
		Config: struct {
			Parameters   string `json:"parameters"`
			Quantization string `json:"quantization"`
			Architecture string `json:"architecture"`
			Size         string `json:"size"`
		}{
			Parameters:   "7B",
			Quantization: "Q8_0",
			Architecture: "transformer",
			Size:         "4.2GB",
		},
	}

	assert.Equal(t, "ghi789", result.ID)
	assert.Equal(t, "latest", result.Tags)
	assert.Equal(t, "2023-01-01T00:00:00Z", result.Created)
	assert.Equal(t, "7B", result.Config.Parameters)
	assert.Equal(t, "Q8_0", result.Config.Quantization)
	assert.Equal(t, "transformer", result.Config.Architecture)
	assert.Equal(t, "4.2GB", result.Config.Size)
}

func TestModelRunnerStatusStruct(t *testing.T) {
	status := ModelRunnerStatus{
		Running: true,
		Status:  "active",
	}

	assert.True(t, status.Running)
	assert.Equal(t, "active", status.Status)
}

// Note: Integration tests for actual Docker command execution would require
// a Docker environment and would be better suited for integration test suites.
// The methods that execute Docker commands (ListModels, ListRunningModels, etc.)
// would need to be tested with a real Docker environment or with more sophisticated
// mocking of the exec.Command functionality.
