import { useMemo } from 'react';
import _ from 'lodash';

import { createOwnershipColumn } from '@/react/docker/components/datatables/createOwnershipColumn';

import { ModelListViewModel } from '../../../types';

import { name } from './name';
import { parameters } from './parameters';
import { quantization } from './quantization';
import { architecture } from './architecture';
import { modelId } from './model-id';
import { created } from './created';
import { size } from './size';
import { state } from './state';
import { quickActions } from './quick-actions';

export function useColumns() {
  return useMemo(
    () =>
      _.compact([
        name,
        parameters,
        quantization,
        architecture,
        modelId,
        created,
        size,
        state,
        quickActions,
        createOwnershipColumn<ModelListViewModel>(),
      ]),
    []
  );
}
