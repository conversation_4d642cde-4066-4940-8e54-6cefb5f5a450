import { Brain } from 'lucide-react';

import { ModelListViewModel } from '@/react/docker/models/types';
import { Environment } from '@/react/portainer/environments/types';

import { Datatable, Table } from '@@/datatables';
import {
  ColumnVisibilityMenu,
  getColumnVisibilityState,
} from '@@/datatables/ColumnVisibilityMenu';
import {
  QuickActionsSettings,
  Action as BaseAction
} from '@@/datatables/QuickActionsSettings';
import { mergeOptions } from '@@/datatables/extend-options/mergeOptions';
import { withColumnFilters } from '@@/datatables/extend-options/withColumnFilters';
import { TableSettingsProvider } from '@@/datatables/useTableSettings';
import { useTableState } from '@@/datatables/useTableState';

import { useModels } from '../../queries/useModels';

import { ModelsDatatableActions } from './ModelsDatatableActions';
import { useColumns } from './columns';
import { createStore } from './datatable-store';
import { QuickAction } from './types';

const storageKey = 'models';
const settingsStore = createStore(storageKey);

function buildAction(id: QuickAction, label: string): BaseAction {
  return { id: id as BaseAction['id'], label };
}

const actions = [
  buildAction('logs', 'Logs'),
  buildAction('inspect', 'Inspect'),
  buildAction('stats', 'Stats'),
  buildAction('console', 'Console'),
];

export interface Props {
  environment: Environment;
}

export function ModelsDatatable({ environment }: Props) {
  const columns = useColumns();
  const tableState = useTableState(settingsStore, storageKey);

  const modelsQuery = useModels(environment.Id, {
    autoRefreshRate: tableState.autoRefreshRate * 1000,
  });

  return (
    <TableSettingsProvider settings={settingsStore}>
      <Datatable
        titleIcon={Brain}
        title="Models"
        settingsManager={tableState}
        columns={columns}
        renderTableActions={(selectedRows) => (
          <ModelsDatatableActions
            selectedItems={selectedRows}
            isAddActionVisible
            endpointId={environment.Id}
          />
        )}
        isLoading={modelsQuery.isLoading}
        isRowSelectable={(row) => !row.original.IsPortainer}
        initialTableState={getColumnVisibilityState(tableState.hiddenColumns)}
        data-cy="docker-models-datatable"
        renderTableSettings={(tableInstance) => (
          <>
            <ColumnVisibilityMenu<ModelListViewModel>
              table={tableInstance}
              onChange={(hiddenColumns) => {
                tableState.setHiddenColumns(hiddenColumns);
              }}
              value={tableState.hiddenColumns}
            />
            <Table.SettingsMenu
              quickActions={<QuickActionsSettings actions={actions} />}
            />
          </>
        )}
        dataset={modelsQuery.data || []}
        extendTableOptions={mergeOptions(
          withColumnFilters(
            tableState.columnFilters,
            tableState.setColumnFilters
          )
        )}
      />
    </TableSettingsProvider>
  );
}
