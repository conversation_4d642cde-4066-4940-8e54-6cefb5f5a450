import { CellContext } from '@tanstack/react-table';

import type { ModelListViewModel } from '@/react/docker/models/types';

import { columnHelper } from './helper';

export const modelId = columnHelper.accessor('ModelId', {
  header: 'Model ID',
  id: 'modelId',
  cell: ModelIdCell,
});

function ModelIdCell({ getValue }: CellContext<ModelListViewModel, string>) {
  const id = getValue();

  return (
    <span className="font-mono text-sm" title={id}>
      {id.substring(0, 12)}
    </span>
  );
}
