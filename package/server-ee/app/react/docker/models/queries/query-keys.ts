import { EnvironmentId } from '@/react/portainer/environments/types';

import { query<PERSON><PERSON>s as dockerQ<PERSON>y<PERSON><PERSON><PERSON> } from '../../queries/utils';

export const queryKeys = {
  list: (environmentId: EnvironmentId) =>
    [...dockerQueryKeys.root(environmentId), 'models'] as const,

  filters: (
    environmentId: EnvironmentId,
    params: { all?: boolean; nodeName?: string } = {}
  ) => [...queryKeys.list(environmentId), params] as const,

  model: (environmentId: EnvironmentId, id: string) =>
    [...queryKeys.list(environmentId), id] as const,

  stats: (environmentId: EnvironmentId, id: string) =>
    [...queryKeys.model(environmentId, id), 'stats'] as const,
};
