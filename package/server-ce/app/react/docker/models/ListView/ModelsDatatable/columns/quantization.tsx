import { CellContext } from '@tanstack/react-table';

import type { ModelListViewModel } from '@/react/docker/models/types';

import { columnHelper } from './helper';

export const quantization = columnHelper.accessor('Quantization', {
  header: 'Quantization',
  id: 'quantization',
  cell: QuantizationCell,
});

function QuantizationCell({
  getValue,
}: CellContext<ModelListViewModel, string>) {
  const quant = getValue();

  return <span className="font-mono text-sm">{quant}</span>;
}
