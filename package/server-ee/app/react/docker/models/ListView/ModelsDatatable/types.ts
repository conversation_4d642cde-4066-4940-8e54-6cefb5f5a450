import {
  BasicTableSettings,
  FilteredColumnsTableSettings,
  RefreshableTableSettings,
  SettableColumnsTableSettings,
} from '@@/datatables/types';

export type QuickAction = 'inspect' | 'logs' | 'stats' | 'console';

export interface SettableQuickActionsTableSettings<TAction> {
  hiddenQuickActions: TAction[];
  setHiddenQuickActions: (hiddenQuickActions: TAction[]) => void;
}

export interface TableSettings
  extends BasicTableSettings,
    SettableColumnsTableSettings,
    SettableQuickActionsTableSettings<QuickAction>,
    RefreshableTableSettings,
    FilteredColumnsTableSettings {
  truncateModelName: number;
  setTruncateModelName: (value: number) => void;
}
