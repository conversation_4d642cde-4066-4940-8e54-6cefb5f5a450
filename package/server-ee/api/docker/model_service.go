package docker

import (
	"bytes"
	"encoding/json"
	"fmt"
	"os/exec"
	"path"
	"runtime"
	"strings"
	"time"

	portaineree "github.com/portainer/portainer-ee/api"
	"github.com/rs/zerolog/log"
)

// ModelService represents a service for managing Docker models
type ModelService struct {
	binaryPath           string
	configPath           string
	reverseTunnelService portaineree.ReverseTunnelService
}

// NewModelService initializes a new ModelService
func NewModelService(binaryPath, configPath string, reverseTunnelService portaineree.ReverseTunnelService) *ModelService {
	return &ModelService{
		binaryPath:           binaryPath,
		configPath:           configPath,
		reverseTunnelService: reverseTunnelService,
	}
}

// ModelListItem represents a model from 'docker model ls' output
type ModelListItem struct {
	ModelName     string    `json:"model_name"`
	Parameters    string    `json:"parameters"`
	Quantization  string    `json:"quantization"`
	Architecture  string    `json:"architecture"`
	ModelID       string    `json:"model_id"`
	Created       time.Time `json:"created"`
	Size          string    `json:"size"`
}

// ModelRunningItem represents a running model from 'docker model ps' output
type ModelRunningItem struct {
	ModelName string `json:"model_name"`
	ModelID   string `json:"model_id"`
	Status    string `json:"status"`
}

// ModelInspectResult represents the result of 'docker model inspect' command
type ModelInspectResult struct {
	ID       string `json:"id"`
	Tags     string `json:"tags"`
	Created  string `json:"created"`
	Config   struct {
		Parameters   string `json:"parameters"`
		Quantization string `json:"quantization"`
		Architecture string `json:"architecture"`
		Size         string `json:"size"`
	} `json:"config"`
}

// ModelRunnerStatus represents the status of the model runner
type ModelRunnerStatus struct {
	Running bool   `json:"running"`
	Status  string `json:"status"`
}

// ListModels executes 'docker model ls' command and returns all models
func (service *ModelService) ListModels(endpoint *portaineree.Endpoint) ([]ModelListItem, error) {
	command, args, err := service.prepareDockerCommandAndArgs(endpoint)
	if err != nil {
		return nil, err
	}

	args = append(args, "model", "ls", "--format", "json")

	output, err := service.runCommand(command, args)
	if err != nil {
		return nil, fmt.Errorf("failed to list models: %w", err)
	}

	var models []ModelListItem
	lines := strings.Split(strings.TrimSpace(string(output)), "\n")
	for _, line := range lines {
		if line == "" {
			continue
		}
		var model ModelListItem
		if err := json.Unmarshal([]byte(line), &model); err != nil {
			log.Warn().Err(err).Str("line", line).Msg("Failed to parse model list item")
			continue
		}
		models = append(models, model)
	}

	return models, nil
}

// ListRunningModels executes 'docker model ps' command and returns running models
func (service *ModelService) ListRunningModels(endpoint *portaineree.Endpoint) ([]ModelRunningItem, error) {
	command, args, err := service.prepareDockerCommandAndArgs(endpoint)
	if err != nil {
		return nil, err
	}

	args = append(args, "model", "ps", "--format", "json")

	output, err := service.runCommand(command, args)
	if err != nil {
		return nil, fmt.Errorf("failed to list running models: %w", err)
	}

	var models []ModelRunningItem
	lines := strings.Split(strings.TrimSpace(string(output)), "\n")
	for _, line := range lines {
		if line == "" {
			continue
		}
		var model ModelRunningItem
		if err := json.Unmarshal([]byte(line), &model); err != nil {
			log.Warn().Err(err).Str("line", line).Msg("Failed to parse running model item")
			continue
		}
		models = append(models, model)
	}

	return models, nil
}

// GetModelRunnerStatus executes 'docker model status' command
func (service *ModelService) GetModelRunnerStatus(endpoint *portaineree.Endpoint) (*ModelRunnerStatus, error) {
	command, args, err := service.prepareDockerCommandAndArgs(endpoint)
	if err != nil {
		return nil, err
	}

	args = append(args, "model", "status")

	output, err := service.runCommand(command, args)
	if err != nil {
		return &ModelRunnerStatus{Running: false, Status: "stopped"}, nil
	}

	status := strings.TrimSpace(string(output))
	running := strings.Contains(strings.ToLower(status), "running")

	return &ModelRunnerStatus{
		Running: running,
		Status:  status,
	}, nil
}

// InstallModelRunner executes 'docker model install-runner' command
func (service *ModelService) InstallModelRunner(endpoint *portaineree.Endpoint) error {
	command, args, err := service.prepareDockerCommandAndArgs(endpoint)
	if err != nil {
		return err
	}

	args = append(args, "model", "install-runner")

	_, err = service.runCommand(command, args)
	if err != nil {
		return fmt.Errorf("failed to install model runner: %w", err)
	}

	return nil
}

// UninstallModelRunner executes 'docker model uninstall-runner' command
func (service *ModelService) UninstallModelRunner(endpoint *portaineree.Endpoint) error {
	command, args, err := service.prepareDockerCommandAndArgs(endpoint)
	if err != nil {
		return err
	}

	args = append(args, "model", "uninstall-runner")

	_, err = service.runCommand(command, args)
	if err != nil {
		return fmt.Errorf("failed to uninstall model runner: %w", err)
	}

	return nil
}

// UnloadModel executes 'docker model unload' command to stop a model
func (service *ModelService) UnloadModel(endpoint *portaineree.Endpoint, modelName string) error {
	command, args, err := service.prepareDockerCommandAndArgs(endpoint)
	if err != nil {
		return err
	}

	args = append(args, "model", "unload", modelName)

	_, err = service.runCommand(command, args)
	if err != nil {
		return fmt.Errorf("failed to unload model %s: %w", modelName, err)
	}

	return nil
}

// RemoveModel executes 'docker model rm' command to remove a model
func (service *ModelService) RemoveModel(endpoint *portaineree.Endpoint, modelName string) error {
	command, args, err := service.prepareDockerCommandAndArgs(endpoint)
	if err != nil {
		return err
	}

	args = append(args, "model", "rm", modelName)

	_, err = service.runCommand(command, args)
	if err != nil {
		return fmt.Errorf("failed to remove model %s: %w", modelName, err)
	}

	return nil
}

// InspectModel executes 'docker model inspect' command to get detailed model information
func (service *ModelService) InspectModel(endpoint *portaineree.Endpoint, modelName string) (*ModelInspectResult, error) {
	command, args, err := service.prepareDockerCommandAndArgs(endpoint)
	if err != nil {
		return nil, err
	}

	args = append(args, "model", "inspect", modelName)

	output, err := service.runCommand(command, args)
	if err != nil {
		return nil, fmt.Errorf("failed to inspect model %s: %w", modelName, err)
	}

	var result ModelInspectResult
	if err := json.Unmarshal(output, &result); err != nil {
		return nil, fmt.Errorf("failed to parse model inspect result: %w", err)
	}

	return &result, nil
}

// prepareDockerCommandAndArgs prepares the Docker command and arguments for execution
func (service *ModelService) prepareDockerCommandAndArgs(endpoint *portaineree.Endpoint) (string, []string, error) {
	// Assume Linux as a default
	command := path.Join(service.binaryPath, "docker")

	if runtime.GOOS == "windows" {
		command = path.Join(service.binaryPath, "docker.exe")
	}

	args := make([]string, 0)
	args = append(args, "--config", service.configPath)

	endpointURL := endpoint.URL
	if endpoint.Type == portaineree.EdgeAgentOnDockerEnvironment {
		tunnelAddr, err := service.reverseTunnelService.TunnelAddr(endpoint)
		if err != nil {
			return "", nil, err
		}

		endpointURL = "tcp://" + tunnelAddr
	}

	if endpointURL != "" && !strings.HasPrefix(endpointURL, "unix://") && !strings.HasPrefix(endpointURL, "npipe://") {
		args = append(args, "--host", endpointURL)
	}

	if endpoint.TLSConfig.TLS {
		args = append(args, "--tls")

		if !endpoint.TLSConfig.TLSSkipVerify {
			args = append(args, "--tlsverify")
		}
	}

	return command, args, nil
}

// runCommand executes a command and returns the output
func (service *ModelService) runCommand(command string, args []string) ([]byte, error) {
	var stderr bytes.Buffer
	cmd := exec.Command(command, args...)
	cmd.Stderr = &stderr

	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("%w: %s", err, stderr.String())
	}

	return output, nil
}
