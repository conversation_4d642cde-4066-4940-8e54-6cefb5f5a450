import { CellContext } from '@tanstack/react-table';
import { Eye, FileText, BarChart3, Terminal } from 'lucide-react';

import { useAuthorizations } from '@/react/hooks/useUser';
import { ModelListViewModel } from '@/react/docker/models/types';

import { Button } from '@@/buttons';

import { columnHelper } from './helper';

export const quickActions = columnHelper.display({
  header: 'Quick Actions',
  id: 'actions',
  cell: QuickActionsCell,
});

function QuickActionsCell({
  row: { original: model },
}: CellContext<ModelListViewModel, unknown>) {
  const { authorized } = useAuthorizations([
    'DockerContainerStats',
    'DockerContainerLogs',
    'DockerExecStart',
    'DockerContainerInspect',
  ]);

  if (!authorized) {
    return null;
  }

  return (
    <div className="flex gap-1">
      <Button
        size="xsmall"
        color="light"
        icon={Eye}
        title="Inspect model"
        data-cy="inspect-model-button"
        onClick={() => {
          // TODO: Implement inspect functionality
          // eslint-disable-next-line no-console
          console.log('Inspect model:', model.Id);
        }}
      />
      <Button
        size="xsmall"
        color="light"
        icon={FileText}
        title="View logs"
        data-cy="view-logs-button"
        onClick={() => {
          // TODO: Implement logs functionality
          // eslint-disable-next-line no-console
          console.log('View logs:', model.Id);
        }}
      />
      <Button
        size="xsmall"
        color="light"
        icon={BarChart3}
        title="View stats"
        data-cy="view-stats-button"
        onClick={() => {
          // TODO: Implement stats functionality
          // eslint-disable-next-line no-console
          console.log('View stats:', model.Id);
        }}
      />
      <Button
        size="xsmall"
        color="light"
        icon={Terminal}
        title="Console"
        data-cy="open-console-button"
        onClick={() => {
          // TODO: Implement console functionality
          // eslint-disable-next-line no-console
          console.log('Open console:', model.Id);
        }}
      />
    </div>
  );
}
