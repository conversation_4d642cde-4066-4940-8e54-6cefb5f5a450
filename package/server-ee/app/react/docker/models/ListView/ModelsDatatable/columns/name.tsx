import { CellContext } from '@tanstack/react-table';
import _ from 'lodash';

import type { ModelListViewModel } from '@/react/docker/models/types';

import { columnHelper } from './helper';

export const name = columnHelper.accessor('ModelName', {
  header: 'Model Name',
  id: 'modelName',
  cell: NameCell,
});

export function NameCell({
  getValue,
}: CellContext<ModelListViewModel, string>) {
  const modelName = getValue();

  // For now, just display the name without linking since we don't have detail views yet
  const shortName = _.truncate(modelName, { length: 60 });

  return (
    <span title={modelName} className="font-mono text-sm">
      {shortName}
    </span>
  );
}
