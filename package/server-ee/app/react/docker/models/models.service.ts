import axios, { parseAxiosError } from '@/portainer/services/axios';
import { EnvironmentId } from '@/react/portainer/environments/types';

import { buildDockerUrl } from '../queries/utils/buildDockerUrl';
import { withAgentTargetHeader } from '../proxy/queries/utils';

import { ModelListViewModel, ModelStatus } from './types';

// Backend response types
interface ModelListResponse {
  model_name: string;
  parameters: string;
  quantization: string;
  architecture: string;
  model_id: string;
  created: string;
  size: string;
  status: string;
}

interface ModelRunnerStatusResponse {
  running: boolean;
  status: string;
}

interface ModelInspectResponse {
  id: string;
  tags: string;
  created: string;
  config: {
    parameters: string;
    quantization: string;
    architecture: string;
    size: string;
  };
}

/**
 * Fetch models from the backend API
 */
export async function getModels(
  environmentId: EnvironmentId,
  { nodeName }: { nodeName?: string } = {}
): Promise<ModelListViewModel[]> {
  try {
    const { data } = await axios.get<ModelListResponse[]>(
      buildDockerUrl(environmentId, 'models'),
      { headers: { ...withAgentTargetHeader(nodeName) } }
    );

    return data.map(transformModelResponse);
  } catch (e) {
    throw parseAxiosError(e, 'Failed to retrieve models');
  }
}

/**
 * Get model runner status
 */
export async function getModelRunnerStatus(
  environmentId: EnvironmentId,
  { nodeName }: { nodeName?: string } = {}
): Promise<ModelRunnerStatusResponse> {
  try {
    const { data } = await axios.get<ModelRunnerStatusResponse>(
      buildDockerUrl(environmentId, 'models', 'runner', 'status'),
      { headers: { ...withAgentTargetHeader(nodeName) } }
    );

    return data;
  } catch (e) {
    throw parseAxiosError(e, 'Failed to retrieve model runner status');
  }
}

/**
 * Install model runner
 */
export async function installModelRunner(
  environmentId: EnvironmentId,
  { nodeName }: { nodeName?: string } = {}
): Promise<void> {
  try {
    await axios.post<void>(
      buildDockerUrl(environmentId, 'models', 'runner', 'install'),
      {},
      { headers: { ...withAgentTargetHeader(nodeName) } }
    );
  } catch (e) {
    throw parseAxiosError(e, 'Failed to install model runner');
  }
}

/**
 * Uninstall model runner
 */
export async function uninstallModelRunner(
  environmentId: EnvironmentId,
  { nodeName }: { nodeName?: string } = {}
): Promise<void> {
  try {
    await axios.post<void>(
      buildDockerUrl(environmentId, 'models', 'runner', 'uninstall'),
      {},
      { headers: { ...withAgentTargetHeader(nodeName) } }
    );
  } catch (e) {
    throw parseAxiosError(e, 'Failed to uninstall model runner');
  }
}

/**
 * Unload (stop) a model
 */
export async function unloadModel(
  environmentId: EnvironmentId,
  modelName: string,
  { nodeName }: { nodeName?: string } = {}
): Promise<void> {
  try {
    await axios.post<void>(
      buildDockerUrl(environmentId, 'models', modelName, 'unload'),
      {},
      { headers: { ...withAgentTargetHeader(nodeName) } }
    );
  } catch (e) {
    throw parseAxiosError(e, 'Failed to unload model');
  }
}

/**
 * Remove a model
 */
export async function removeModel(
  environmentId: EnvironmentId,
  modelName: string,
  { nodeName }: { nodeName?: string } = {}
): Promise<void> {
  try {
    await axios.delete<void>(
      buildDockerUrl(environmentId, 'models', modelName, 'remove'),
      { headers: { ...withAgentTargetHeader(nodeName) } }
    );
  } catch (e) {
    throw parseAxiosError(e, 'Failed to remove model');
  }
}

/**
 * Inspect a model
 */
export async function inspectModel(
  environmentId: EnvironmentId,
  modelName: string,
  { nodeName }: { nodeName?: string } = {}
): Promise<ModelInspectResponse> {
  try {
    const { data } = await axios.get<ModelInspectResponse>(
      buildDockerUrl(environmentId, 'models', modelName, 'inspect'),
      { headers: { ...withAgentTargetHeader(nodeName) } }
    );

    return data;
  } catch (e) {
    throw parseAxiosError(e, 'Failed to inspect model');
  }
}

/**
 * Transform backend model response to frontend view model
 */
function transformModelResponse(response: ModelListResponse): ModelListViewModel {
  const status = response.status === 'running' ? ModelStatus.Running : ModelStatus.Stopped;
  const statusText = response.status === 'running' ? 'Running' : 'Stopped';

  return {
    // Core Docker model properties
    ModelName: response.model_name,
    Parameters: response.parameters,
    Quantization: response.quantization,
    Architecture: response.architecture,
    ModelId: response.model_id,
    Created: new Date(response.created).getTime(),
    Size: response.size,

    // UI properties
    Status: status,
    StatusText: statusText,
    NodeName: '',
    IsPortainer: false,

    // Legacy compatibility
    Id: response.model_id,
    Names: [response.model_name],
  };
}
