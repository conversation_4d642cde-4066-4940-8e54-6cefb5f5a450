import { Environment } from '@/react/portainer/environments/types';

import { PageHeader } from '@@/PageHeader';

import { ModelsDatatable } from './ModelsDatatable';

interface Props {
  endpoint: Environment;
}

export function ListView({ endpoint: environment }: Props) {
  return (
    <>
      <PageHeader
        title="Model list"
        breadcrumbs={[{ label: 'Models' }]}
        reload
      />

      <ModelsDatatable environment={environment} />
    </>
  );
}
