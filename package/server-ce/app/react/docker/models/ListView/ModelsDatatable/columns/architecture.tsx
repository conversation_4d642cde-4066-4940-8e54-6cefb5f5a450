import { CellContext } from '@tanstack/react-table';

import type { ModelListViewModel } from '@/react/docker/models/types';

import { columnHelper } from './helper';

export const architecture = columnHelper.accessor('Architecture', {
  header: 'Architecture',
  id: 'architecture',
  cell: ArchitectureCell,
});

function ArchitectureCell({
  getValue,
}: CellContext<ModelListViewModel, string>) {
  const arch = getValue();

  return <span className="font-mono text-sm">{arch}</span>;
}
